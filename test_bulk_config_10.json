{"api_calls": {"host_objects": {"data": [{"name": "BulkTestHost_001", "type": "Host", "value": "***********", "description": "Bulk API test host 1"}, {"name": "BulkTestHost_002", "type": "Host", "value": "***********", "description": "Bulk API test host 2"}, {"name": "BulkTestHost_003", "type": "Host", "value": "***********", "description": "Bulk API test host 3"}, {"name": "BulkTestHost_004", "type": "Host", "value": "***********", "description": "Bulk API test host 4"}, {"name": "BulkTestHost_005", "type": "Host", "value": "***********", "description": "Bulk API test host 5"}, {"name": "BulkTestHost_006", "type": "Host", "value": "***********", "description": "Bulk API test host 6"}, {"name": "BulkTestHost_007", "type": "Host", "value": "***********", "description": "Bulk API test host 7"}, {"name": "BulkTestHost_008", "type": "Host", "value": "***********", "description": "Bulk API test host 8"}, {"name": "BulkTestHost_009", "type": "Host", "value": "***********", "description": "Bulk API test host 9"}, {"name": "BulkTestHost_010", "type": "Host", "value": "***********0", "description": "Bulk API test host 10"}]}, "network_objects": {"data": [{"name": "BulkTestNet_001", "type": "Network", "value": "********/24", "description": "Bulk API test network 1"}, {"name": "BulkTestNet_002", "type": "Network", "value": "********/24", "description": "Bulk API test network 2"}, {"name": "BulkTestNet_003", "type": "Network", "value": "********/24", "description": "Bulk API test network 3"}, {"name": "BulkTestNet_004", "type": "Network", "value": "********/24", "description": "Bulk API test network 4"}, {"name": "BulkTestNet_005", "type": "Network", "value": "********/24", "description": "Bulk API test network 5"}, {"name": "BulkTestNet_006", "type": "Network", "value": "********/24", "description": "Bulk API test network 6"}, {"name": "BulkTestNet_007", "type": "Network", "value": "********/24", "description": "Bulk API test network 7"}, {"name": "BulkTestNet_008", "type": "Network", "value": "********/24", "description": "Bulk API test network 8"}, {"name": "BulkTestNet_009", "type": "Network", "value": "********/24", "description": "Bulk API test network 9"}, {"name": "BulkTestNet_010", "type": "Network", "value": "*********/24", "description": "Bulk API test network 10"}]}, "service_objects": {"data": [{"name": "BulkTestSvc_001", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8000", "description": "Bulk API test service 1"}, {"name": "BulkTestSvc_002", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8001", "description": "Bulk API test service 2"}, {"name": "BulkTestSvc_003", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8002", "description": "Bulk API test service 3"}, {"name": "BulkTestSvc_004", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8003", "description": "Bulk API test service 4"}, {"name": "BulkTestSvc_005", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8004", "description": "Bulk API test service 5"}, {"name": "BulkTestSvc_006", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8005", "description": "Bulk API test service 6"}, {"name": "BulkTestSvc_007", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8006", "description": "Bulk API test service 7"}, {"name": "BulkTestSvc_008", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8007", "description": "Bulk API test service 8"}, {"name": "BulkTestSvc_009", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8008", "description": "Bulk API test service 9"}, {"name": "BulkTestSvc_010", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8009", "description": "Bulk API test service 10"}]}}}