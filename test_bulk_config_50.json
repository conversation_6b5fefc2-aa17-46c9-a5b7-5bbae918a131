{"api_calls": {"host_objects": {"data": [{"name": "BulkTestHost_001", "type": "Host", "value": "***********", "description": "Bulk API test host 1"}, {"name": "BulkTestHost_002", "type": "Host", "value": "***********", "description": "Bulk API test host 2"}, {"name": "BulkTestHost_003", "type": "Host", "value": "***********", "description": "Bulk API test host 3"}, {"name": "BulkTestHost_004", "type": "Host", "value": "***********", "description": "Bulk API test host 4"}, {"name": "BulkTestHost_005", "type": "Host", "value": "***********", "description": "Bulk API test host 5"}, {"name": "BulkTestHost_006", "type": "Host", "value": "***********", "description": "Bulk API test host 6"}, {"name": "BulkTestHost_007", "type": "Host", "value": "***********", "description": "Bulk API test host 7"}, {"name": "BulkTestHost_008", "type": "Host", "value": "***********", "description": "Bulk API test host 8"}, {"name": "BulkTestHost_009", "type": "Host", "value": "***********", "description": "Bulk API test host 9"}, {"name": "BulkTestHost_010", "type": "Host", "value": "***********0", "description": "Bulk API test host 10"}, {"name": "BulkTestHost_011", "type": "Host", "value": "***********1", "description": "Bulk API test host 11"}, {"name": "BulkTestHost_012", "type": "Host", "value": "***********2", "description": "Bulk API test host 12"}, {"name": "BulkTestHost_013", "type": "Host", "value": "***********3", "description": "Bulk API test host 13"}, {"name": "BulkTestHost_014", "type": "Host", "value": "***********4", "description": "Bulk API test host 14"}, {"name": "BulkTestHost_015", "type": "Host", "value": "***********5", "description": "Bulk API test host 15"}, {"name": "BulkTestHost_016", "type": "Host", "value": "***********6", "description": "Bulk API test host 16"}, {"name": "BulkTestHost_017", "type": "Host", "value": "***********7", "description": "Bulk API test host 17"}, {"name": "BulkTestHost_018", "type": "Host", "value": "***********8", "description": "Bulk API test host 18"}, {"name": "BulkTestHost_019", "type": "Host", "value": "***********9", "description": "Bulk API test host 19"}, {"name": "BulkTestHost_020", "type": "Host", "value": "***********0", "description": "Bulk API test host 20"}, {"name": "BulkTestHost_021", "type": "Host", "value": "***********1", "description": "Bulk API test host 21"}, {"name": "BulkTestHost_022", "type": "Host", "value": "***********2", "description": "Bulk API test host 22"}, {"name": "BulkTestHost_023", "type": "Host", "value": "***********3", "description": "Bulk API test host 23"}, {"name": "BulkTestHost_024", "type": "Host", "value": "***********4", "description": "Bulk API test host 24"}, {"name": "BulkTestHost_025", "type": "Host", "value": "***********5", "description": "Bulk API test host 25"}, {"name": "BulkTestHost_026", "type": "Host", "value": "***********6", "description": "Bulk API test host 26"}, {"name": "BulkTestHost_027", "type": "Host", "value": "***********7", "description": "Bulk API test host 27"}, {"name": "BulkTestHost_028", "type": "Host", "value": "***********8", "description": "Bulk API test host 28"}, {"name": "BulkTestHost_029", "type": "Host", "value": "***********9", "description": "Bulk API test host 29"}, {"name": "BulkTestHost_030", "type": "Host", "value": "***********0", "description": "Bulk API test host 30"}, {"name": "BulkTestHost_031", "type": "Host", "value": "***********1", "description": "Bulk API test host 31"}, {"name": "BulkTestHost_032", "type": "Host", "value": "***********2", "description": "Bulk API test host 32"}, {"name": "BulkTestHost_033", "type": "Host", "value": "***********3", "description": "Bulk API test host 33"}, {"name": "BulkTestHost_034", "type": "Host", "value": "***********4", "description": "Bulk API test host 34"}, {"name": "BulkTestHost_035", "type": "Host", "value": "***********5", "description": "Bulk API test host 35"}, {"name": "BulkTestHost_036", "type": "Host", "value": "***********6", "description": "Bulk API test host 36"}, {"name": "BulkTestHost_037", "type": "Host", "value": "***********7", "description": "Bulk API test host 37"}, {"name": "BulkTestHost_038", "type": "Host", "value": "***********8", "description": "Bulk API test host 38"}, {"name": "BulkTestHost_039", "type": "Host", "value": "***********9", "description": "Bulk API test host 39"}, {"name": "BulkTestHost_040", "type": "Host", "value": "***********0", "description": "Bulk API test host 40"}, {"name": "BulkTestHost_041", "type": "Host", "value": "***********1", "description": "Bulk API test host 41"}, {"name": "BulkTestHost_042", "type": "Host", "value": "***********2", "description": "Bulk API test host 42"}, {"name": "BulkTestHost_043", "type": "Host", "value": "***********3", "description": "Bulk API test host 43"}, {"name": "BulkTestHost_044", "type": "Host", "value": "***********4", "description": "Bulk API test host 44"}, {"name": "BulkTestHost_045", "type": "Host", "value": "***********5", "description": "Bulk API test host 45"}, {"name": "BulkTestHost_046", "type": "Host", "value": "***********6", "description": "Bulk API test host 46"}, {"name": "BulkTestHost_047", "type": "Host", "value": "***********7", "description": "Bulk API test host 47"}, {"name": "BulkTestHost_048", "type": "Host", "value": "***********8", "description": "Bulk API test host 48"}, {"name": "BulkTestHost_049", "type": "Host", "value": "***********9", "description": "Bulk API test host 49"}, {"name": "BulkTestHost_050", "type": "Host", "value": "***********0", "description": "Bulk API test host 50"}]}, "network_objects": {"data": [{"name": "BulkTestNet_001", "type": "Network", "value": "********/24", "description": "Bulk API test network 1"}, {"name": "BulkTestNet_002", "type": "Network", "value": "********/24", "description": "Bulk API test network 2"}, {"name": "BulkTestNet_003", "type": "Network", "value": "********/24", "description": "Bulk API test network 3"}, {"name": "BulkTestNet_004", "type": "Network", "value": "********/24", "description": "Bulk API test network 4"}, {"name": "BulkTestNet_005", "type": "Network", "value": "********/24", "description": "Bulk API test network 5"}, {"name": "BulkTestNet_006", "type": "Network", "value": "********/24", "description": "Bulk API test network 6"}, {"name": "BulkTestNet_007", "type": "Network", "value": "********/24", "description": "Bulk API test network 7"}, {"name": "BulkTestNet_008", "type": "Network", "value": "********/24", "description": "Bulk API test network 8"}, {"name": "BulkTestNet_009", "type": "Network", "value": "********/24", "description": "Bulk API test network 9"}, {"name": "BulkTestNet_010", "type": "Network", "value": "*********/24", "description": "Bulk API test network 10"}, {"name": "BulkTestNet_011", "type": "Network", "value": "*********/24", "description": "Bulk API test network 11"}, {"name": "BulkTestNet_012", "type": "Network", "value": "*********/24", "description": "Bulk API test network 12"}, {"name": "BulkTestNet_013", "type": "Network", "value": "*********/24", "description": "Bulk API test network 13"}, {"name": "BulkTestNet_014", "type": "Network", "value": "*********/24", "description": "Bulk API test network 14"}, {"name": "BulkTestNet_015", "type": "Network", "value": "*********/24", "description": "Bulk API test network 15"}, {"name": "BulkTestNet_016", "type": "Network", "value": "*********/24", "description": "Bulk API test network 16"}, {"name": "BulkTestNet_017", "type": "Network", "value": "*********/24", "description": "Bulk API test network 17"}, {"name": "BulkTestNet_018", "type": "Network", "value": "*********/24", "description": "Bulk API test network 18"}, {"name": "BulkTestNet_019", "type": "Network", "value": "*********/24", "description": "Bulk API test network 19"}, {"name": "BulkTestNet_020", "type": "Network", "value": "*********/24", "description": "Bulk API test network 20"}, {"name": "BulkTestNet_021", "type": "Network", "value": "*********/24", "description": "Bulk API test network 21"}, {"name": "BulkTestNet_022", "type": "Network", "value": "*********/24", "description": "Bulk API test network 22"}, {"name": "BulkTestNet_023", "type": "Network", "value": "*********/24", "description": "Bulk API test network 23"}, {"name": "BulkTestNet_024", "type": "Network", "value": "*********/24", "description": "Bulk API test network 24"}, {"name": "BulkTestNet_025", "type": "Network", "value": "*********/24", "description": "Bulk API test network 25"}, {"name": "BulkTestNet_026", "type": "Network", "value": "*********/24", "description": "Bulk API test network 26"}, {"name": "BulkTestNet_027", "type": "Network", "value": "*********/24", "description": "Bulk API test network 27"}, {"name": "BulkTestNet_028", "type": "Network", "value": "*********/24", "description": "Bulk API test network 28"}, {"name": "BulkTestNet_029", "type": "Network", "value": "*********/24", "description": "Bulk API test network 29"}, {"name": "BulkTestNet_030", "type": "Network", "value": "*********/24", "description": "Bulk API test network 30"}, {"name": "BulkTestNet_031", "type": "Network", "value": "*********/24", "description": "Bulk API test network 31"}, {"name": "BulkTestNet_032", "type": "Network", "value": "*********/24", "description": "Bulk API test network 32"}, {"name": "BulkTestNet_033", "type": "Network", "value": "*********/24", "description": "Bulk API test network 33"}, {"name": "BulkTestNet_034", "type": "Network", "value": "*********/24", "description": "Bulk API test network 34"}, {"name": "BulkTestNet_035", "type": "Network", "value": "*********/24", "description": "Bulk API test network 35"}, {"name": "BulkTestNet_036", "type": "Network", "value": "*********/24", "description": "Bulk API test network 36"}, {"name": "BulkTestNet_037", "type": "Network", "value": "*********/24", "description": "Bulk API test network 37"}, {"name": "BulkTestNet_038", "type": "Network", "value": "*********/24", "description": "Bulk API test network 38"}, {"name": "BulkTestNet_039", "type": "Network", "value": "*********/24", "description": "Bulk API test network 39"}, {"name": "BulkTestNet_040", "type": "Network", "value": "*********/24", "description": "Bulk API test network 40"}, {"name": "BulkTestNet_041", "type": "Network", "value": "*********/24", "description": "Bulk API test network 41"}, {"name": "BulkTestNet_042", "type": "Network", "value": "*********/24", "description": "Bulk API test network 42"}, {"name": "BulkTestNet_043", "type": "Network", "value": "*********/24", "description": "Bulk API test network 43"}, {"name": "BulkTestNet_044", "type": "Network", "value": "*********/24", "description": "Bulk API test network 44"}, {"name": "BulkTestNet_045", "type": "Network", "value": "*********/24", "description": "Bulk API test network 45"}, {"name": "BulkTestNet_046", "type": "Network", "value": "*********/24", "description": "Bulk API test network 46"}, {"name": "BulkTestNet_047", "type": "Network", "value": "*********/24", "description": "Bulk API test network 47"}, {"name": "BulkTestNet_048", "type": "Network", "value": "*********/24", "description": "Bulk API test network 48"}, {"name": "BulkTestNet_049", "type": "Network", "value": "*********/24", "description": "Bulk API test network 49"}, {"name": "BulkTestNet_050", "type": "Network", "value": "*********/24", "description": "Bulk API test network 50"}]}, "service_objects": {"data": [{"name": "BulkTestSvc_001", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8000", "description": "Bulk API test service 1"}, {"name": "BulkTestSvc_002", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8001", "description": "Bulk API test service 2"}, {"name": "BulkTestSvc_003", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8002", "description": "Bulk API test service 3"}, {"name": "BulkTestSvc_004", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8003", "description": "Bulk API test service 4"}, {"name": "BulkTestSvc_005", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8004", "description": "Bulk API test service 5"}, {"name": "BulkTestSvc_006", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8005", "description": "Bulk API test service 6"}, {"name": "BulkTestSvc_007", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8006", "description": "Bulk API test service 7"}, {"name": "BulkTestSvc_008", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8007", "description": "Bulk API test service 8"}, {"name": "BulkTestSvc_009", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8008", "description": "Bulk API test service 9"}, {"name": "BulkTestSvc_010", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8009", "description": "Bulk API test service 10"}, {"name": "BulkTestSvc_011", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8010", "description": "Bulk API test service 11"}, {"name": "BulkTestSvc_012", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8011", "description": "Bulk API test service 12"}, {"name": "BulkTestSvc_013", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8012", "description": "Bulk API test service 13"}, {"name": "BulkTestSvc_014", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8013", "description": "Bulk API test service 14"}, {"name": "BulkTestSvc_015", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8014", "description": "Bulk API test service 15"}, {"name": "BulkTestSvc_016", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8015", "description": "Bulk API test service 16"}, {"name": "BulkTestSvc_017", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8016", "description": "Bulk API test service 17"}, {"name": "BulkTestSvc_018", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8017", "description": "Bulk API test service 18"}, {"name": "BulkTestSvc_019", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8018", "description": "Bulk API test service 19"}, {"name": "BulkTestSvc_020", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8019", "description": "Bulk API test service 20"}, {"name": "BulkTestSvc_021", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8020", "description": "Bulk API test service 21"}, {"name": "BulkTestSvc_022", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8021", "description": "Bulk API test service 22"}, {"name": "BulkTestSvc_023", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8022", "description": "Bulk API test service 23"}, {"name": "BulkTestSvc_024", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8023", "description": "Bulk API test service 24"}, {"name": "BulkTestSvc_025", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8024", "description": "Bulk API test service 25"}, {"name": "BulkTestSvc_026", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8025", "description": "Bulk API test service 26"}, {"name": "BulkTestSvc_027", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8026", "description": "Bulk API test service 27"}, {"name": "BulkTestSvc_028", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8027", "description": "Bulk API test service 28"}, {"name": "BulkTestSvc_029", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8028", "description": "Bulk API test service 29"}, {"name": "BulkTestSvc_030", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8029", "description": "Bulk API test service 30"}, {"name": "BulkTestSvc_031", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8030", "description": "Bulk API test service 31"}, {"name": "BulkTestSvc_032", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8031", "description": "Bulk API test service 32"}, {"name": "BulkTestSvc_033", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8032", "description": "Bulk API test service 33"}, {"name": "BulkTestSvc_034", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8033", "description": "Bulk API test service 34"}, {"name": "BulkTestSvc_035", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8034", "description": "Bulk API test service 35"}, {"name": "BulkTestSvc_036", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8035", "description": "Bulk API test service 36"}, {"name": "BulkTestSvc_037", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8036", "description": "Bulk API test service 37"}, {"name": "BulkTestSvc_038", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8037", "description": "Bulk API test service 38"}, {"name": "BulkTestSvc_039", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8038", "description": "Bulk API test service 39"}, {"name": "BulkTestSvc_040", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8039", "description": "Bulk API test service 40"}, {"name": "BulkTestSvc_041", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8040", "description": "Bulk API test service 41"}, {"name": "BulkTestSvc_042", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8041", "description": "Bulk API test service 42"}, {"name": "BulkTestSvc_043", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8042", "description": "Bulk API test service 43"}, {"name": "BulkTestSvc_044", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8043", "description": "Bulk API test service 44"}, {"name": "BulkTestSvc_045", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8044", "description": "Bulk API test service 45"}, {"name": "BulkTestSvc_046", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8045", "description": "Bulk API test service 46"}, {"name": "BulkTestSvc_047", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8046", "description": "Bulk API test service 47"}, {"name": "BulkTestSvc_048", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8047", "description": "Bulk API test service 48"}, {"name": "BulkTestSvc_049", "type": "ProtocolPortObject", "protocol": "TCP", "port": "8048", "description": "Bulk API test service 49"}, {"name": "BulkTestSvc_050", "type": "ProtocolPortObject", "protocol": "UDP", "port": "8049", "description": "Bulk API test service 50"}]}}}